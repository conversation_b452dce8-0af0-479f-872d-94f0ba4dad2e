@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here.
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 47.4% 11.2%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 47.4% 11.2%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 47.4% 11.2%;

    /* Fresh Green Brand Palette from Logo */
    --primary: 108 60% 45%;
    --primary-foreground: 0 0% 100%;

    --secondary: 60 20% 88%;
    --secondary-foreground: 108 25% 25%;

    --muted: 60 15% 92%;
    --muted-foreground: 108 15% 50%;

    --accent: 100 45% 40%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 108 60% 45%;

    --radius: 0.75rem;

    /* Brand tokens - Fresh Green Theme */
    --brand: 108 60% 45%;
    --brand-2: 100 45% 40%;
    --primary-glow: 108 60% 55%;

    /* Enhanced Typography Scale */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    --font-size-6xl: 3.75rem;
    --font-size-7xl: 4.5rem;

    /* Consistent Spacing System */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 2.5rem;
    --spacing-3xl: 3rem;
    --spacing-4xl: 4rem;
    --spacing-5xl: 5rem;
    --spacing-6xl: 6rem;

    /* Gradients & Shadows */
    --gradient-hero: linear-gradient(135deg, hsl(var(--brand)) 0%, hsl(var(--brand-2)) 100%);
    --gradient-surface: radial-gradient(800px 400px at var(--mouse-x, 50%) var(--mouse-y, 50%), hsl(var(--brand) / 0.08), transparent 60%);

    --shadow-elegant: 0 10px 30px -12px hsl(var(--brand) / 0.25);
    --shadow-glow: 0 0 40px hsl(var(--primary-glow) / 0.4);

    /* Sidebar */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: var(--brand);
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 108 50% 50%;
    --primary-foreground: 0 0% 100%;

    --secondary: 108 15% 20%;
    --secondary-foreground: 60 20% 90%;

    --muted: 108 10% 25%;
    --muted-foreground: 108 20% 70%;

    --accent: 100 40% 45%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 215 92% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Gradients & Shadows */
    --gradient-hero: linear-gradient(135deg, hsl(var(--brand-2)) 0%, hsl(var(--brand)) 100%);
    --gradient-surface: radial-gradient(800px 400px at var(--mouse-x, 50%) var(--mouse-y, 50%), hsl(var(--brand) / 0.12), transparent 60%);
    --gradient-card: linear-gradient(145deg, hsl(var(--card)) 0%, hsl(var(--muted)) 100%);
    --gradient-button: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--accent)) 100%);
    --shadow-elegant: 0 10px 30px -12px hsl(var(--brand) / 0.3);
    --shadow-glow: 0 0 50px hsl(var(--primary-glow) / 0.5);
    --shadow-card: 0 4px 20px -4px hsl(var(--foreground) / 0.1);
    --shadow-card-hover: 0 8px 30px -8px hsl(var(--brand) / 0.2);

    /* Animation Durations */
    --duration-fast: 150ms;
    --duration-normal: 300ms;
    --duration-slow: 500ms;

    /* Easing Functions */
    --ease-out-cubic: cubic-bezier(0.33, 1, 0.68, 1);
    --ease-in-out-cubic: cubic-bezier(0.65, 0, 0.35, 1);
    --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  /* Enhanced Background Utilities */
  .bg-hero {
    background-image: var(--gradient-hero);
  }
  .bg-card-gradient {
    background-image: var(--gradient-card);
  }
  .bg-button-gradient {
    background-image: var(--gradient-button);
  }

  /* Enhanced Shadow Utilities */
  .shadow-elegant {
    box-shadow: var(--shadow-elegant);
  }
  .shadow-card {
    box-shadow: var(--shadow-card);
  }
  .shadow-card-hover {
    box-shadow: var(--shadow-card-hover);
  }

  /* Interactive Surface with Enhanced Animation */
  .interactive-surface {
    position: relative;
    overflow: hidden;
    transition: transform var(--duration-normal) var(--ease-out-cubic);
  }
  .interactive-surface::after {
    content: "";
    position: absolute;
    inset: -1px;
    background: var(--gradient-surface);
    pointer-events: none;
    transition: opacity var(--duration-normal) var(--ease-out-cubic);
    opacity: 0;
  }
  .interactive-surface:hover::after {
    opacity: 1;
  }
  .interactive-surface:hover {
    transform: translateY(-2px);
  }

  /* Enhanced Button States */
  .btn-enhanced {
    position: relative;
    overflow: hidden;
    transition: all var(--duration-normal) var(--ease-out-cubic);
  }
  .btn-enhanced::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left var(--duration-slow) var(--ease-out-cubic);
  }
  .btn-enhanced:hover::before {
    left: 100%;
  }
  .btn-enhanced:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-card-hover);
  }
  .btn-enhanced:active {
    transform: translateY(0);
  }

  /* Card Hover Effects */
  .card-enhanced {
    transition: all var(--duration-normal) var(--ease-out-cubic);
    cursor: pointer;
  }
  .card-enhanced:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: var(--shadow-card-hover);
  }

  /* Typography Utilities */
  .text-gradient {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--accent)) 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  /* Loading Animation */
  .loading-pulse {
    animation: loading-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* Bounce Animation for CTAs */
  .bounce-subtle {
    animation: bounce-subtle 2s infinite;
  }

  /* Fade In Animation */
  .fade-in {
    animation: fade-in 0.6s var(--ease-out-cubic) forwards;
  }

  /* Slide Up Animation */
  .slide-up {
    animation: slide-up 0.6s var(--ease-out-cubic) forwards;
  }
}

/* Enhanced Keyframe Animations */
@keyframes loading-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce-subtle {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Accessibility: Respect reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .interactive-surface::after,
  .btn-enhanced,
  .card-enhanced,
  .loading-pulse,
  .bounce-subtle,
  .fade-in,
  .slide-up {
    animation: none !important;
    transition: none !important;
  }

  .interactive-surface:hover,
  .btn-enhanced:hover,
  .card-enhanced:hover {
    transform: none !important;
  }
}
